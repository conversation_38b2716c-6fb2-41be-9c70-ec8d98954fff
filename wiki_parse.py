from urllib.parse import urlparse, parse_qs

from atlassian import Confluence
from bs4 import BeautifulSoup
import json
import re
import os
from datetime import datetime
import base64
import requests
from urllib.parse import quote


# Confluence连接配置
CONFLUENCE_URL = 'http://wiki.fengyuwl.net/'
USERNAME = 'zhoufei'
API_TOKEN = 'MDE2MjkzMzE4MzQ3OjmIrSEtfzIxb7Rvwi4BQ6Vy89Bl'  # 从Atlassian账户设置生成
PASSWORD = "abc123456"
# SPACE_KEY = 'SPACE_KEY'  # Confluence空间标识
# PAGE_TITLE = 'Your Page Title'  # 目标页面标题

# 初始化Confluence客户端
confluence = Confluence(
    url=CONFLUENCE_URL,
    username=USERNAME,
    password=PASSWORD
)


def extract_page_id(url):
    # 解析URL
    parsed_url = urlparse(url)
    # 解析查询参数
    query_params = parse_qs(parsed_url.query)
    # 获取pageId参数的值（返回的是列表）
    page_ids = query_params.get('pageId', [])

    if page_ids:
        # 返回第一个pageId值（字符串）
        return page_ids[0]
    else:
        return None


def parse_page_content(images_dir=None):
    """获取并解析Confluence页面内容"""
    try:
        # 获取页面ID
        # page_id = confluence.get_page_id(SPACE_KEY, PAGE_TITLE)
        page_id = extract_page_id(page_url)
        if not page_id:
            return {"error": "Page not found"}

        # 获取页面详细信息
        page_info = confluence.get_page_by_id(page_id, expand='version,history,body.storage')

        # 获取页面内容（Storage格式）
        storage_content = page_info['body']['storage']['value']

        # 临时调试：检查原始XML中的所有图片相关元素
        print("=== 调试：检查原始XML中的图片元素 ===")
        soup_debug = BeautifulSoup(storage_content, 'html.parser')

        # 查找所有可能的图片元素
        ac_images = soup_debug.find_all('ac:image')
        print(f"找到 {len(ac_images)} 个 ac:image 元素:")
        for i, img in enumerate(ac_images):
            print(f"  {i+1}. {img}")
            # 检查图片的完整父元素链
            current = img
            level = 0
            while current.parent and level < 5:
                current = current.parent
                level += 1
                print(f"      第{level}级父元素: {current.name} - {current.get('ac:name', '')} - {current.get('class', '')}")

        # 查找所有div.content-wrapper元素
        content_wrappers = soup_debug.find_all('div', class_='content-wrapper')
        print(f"\n找到 {len(content_wrappers)} 个 div.content-wrapper 元素:")
        for i, div in enumerate(content_wrappers):
            print(f"  {i+1}. 父元素: {div.parent.name if div.parent else 'None'}")
            images_in_div = div.find_all('ac:image')
            print(f"      包含 {len(images_in_div)} 个图片")

        # 检查表格结构
        tables = soup_debug.find_all('table')
        print(f"\n找到 {len(tables)} 个表格:")
        for i, table in enumerate(tables):
            tds = table.find_all('td')
            divs_in_table = table.find_all('div', class_='content-wrapper')
            images_in_table = table.find_all('ac:image')
            print(f"  表格 {i+1}: {len(tds)} 个单元格, {len(divs_in_table)} 个div.content-wrapper, {len(images_in_table)} 个图片")

            # 如果表格中有div.content-wrapper，详细检查
            if divs_in_table:
                print(f"    详细检查包含div.content-wrapper的单元格:")
                for j, td in enumerate(tds):
                    divs_in_td = td.find_all('div', class_='content-wrapper')
                    images_in_td = td.find_all('ac:image')
                    if divs_in_td or images_in_td:
                        print(f"      单元格 {j+1}: {len(divs_in_td)} 个div, {len(images_in_td)} 个图片")
                        if divs_in_td:
                            print(f"        div的直接子元素: {[c.name for c in divs_in_td[0].children if c.name]}")
                break  # 只详细检查第一个包含图片的表格

        # 查找所有附件引用
        ri_attachments = soup_debug.find_all('ri:attachment')
        print(f"找到 {len(ri_attachments)} 个 ri:attachment 元素:")
        for i, att in enumerate(ri_attachments):
            print(f"  {i+1}. {att}")

        # 查找所有URL引用
        ri_urls = soup_debug.find_all('ri:url')
        print(f"找到 {len(ri_urls)} 个 ri:url 元素:")
        for i, url in enumerate(ri_urls):
            print(f"  {i+1}. {url}")

        print("=== 调试结束 ===\n")

        # 使用BeautifulSoup解析XML
        # soup = BeautifulSoup(storage_content, 'xml')
        # soup = BeautifulSoup(storage_content, 'lxml')
        soup = BeautifulSoup(storage_content, 'html.parser')
        # print("实际解析出的 XML 结构: " + "\n*********************" + soup.prettify() + "\n*********************")
        structured_data = []

        # 遍历所有顶级元素
        for element in soup.find_all(recursive=False):
            element_data = parse_element(element, images_dir)
            if element_data:
                structured_data.append(element_data)

        # 添加页面元数据
        metadata = {
            "page_id": page_info['id'],
            "title": page_info['title'],
            "version": page_info['version']['number'],
            "creator": page_info['history']['createdBy']['displayName'],
            "created_date": page_info['history']['createdDate'],
            "last_modified_by": page_info['version']['by']['displayName'],
            "last_modified_date": page_info['version']['when']
        }

        return {
            "metadata": metadata,
            "content": structured_data
        }
    except Exception as e:
        return {"error": str(e)}

def parse_element(element, images_dir=None):
    """解析单个XML元素并返回结构化数据"""
    # 临时调试：记录所有被处理的元素
    if element.name == 'div' and 'content-wrapper' in element.get('class', []):
        print(f"DEBUG: parse_element 处理 div.content-wrapper")

    # 处理标题（h1-h6）
    if element.name.startswith('h'):
        level = int(element.name[1])
        return {
            "type": "heading",
            "level": level,
            "text": clean_text(element.get_text()),
            "id": element.get('id', '')
        }

    # 处理段落
    elif element.name == 'p':
        return parse_paragraph(element, images_dir)

    # 处理列表
    elif element.name in ['ul', 'ol']:
        return parse_list(element, images_dir)

    # 处理表格
    elif element.name == 'table':
        print(f"DEBUG: parse_element 处理表格元素")
        return parse_table(element, images_dir)

    # 处理代码块（Confluence宏）
    elif element.name == 'ac:structured-macro' and element.get('ac:name') == 'code':
        return parse_code_macro(element)

    # 处理信息宏（info、note、warning）
    elif element.name == 'ac:structured-macro' and element.get('ac:name') in ['info', 'note', 'warning', 'tip']:
        return parse_info_macro(element, images_dir)

    # 处理面板宏
    elif element.name == 'ac:structured-macro' and element.get('ac:name') == 'panel':
        return parse_panel_macro(element, images_dir)

    # 处理图片
    elif element.name == 'ac:image':
        return parse_image(element, images_dir)

    # 处理附件
    elif element.name == 'ac:link' and element.find('ri:attachment'):
        return parse_attachment(element)

    # 处理链接
    elif element.name == 'a':
        return parse_link(element)

    # 处理引用
    elif element.name == 'blockquote':
        return {
            "type": "blockquote",
            "content": [parse_element(child, images_dir) for child in element.children if child.name]
        }

    # 处理水平线
    elif element.name == 'hr':
        return {"type": "horizontal_rule"}

    # 处理div容器（如content-wrapper）
    elif element.name == 'div':
        # 递归处理div中的子元素
        content = []
        for child in element.children:
            if child.name:
                print(f"DEBUG: div处理子元素 {child.name}")
                parsed = parse_element(child, images_dir)
                if parsed:
                    content.append(parsed)
                    print(f"DEBUG: 添加到div内容: {parsed.get('type', 'unknown')}")
                    # 如果是段落，检查其内容
                    if parsed.get('type') == 'paragraph' and 'content' in parsed:
                        para_images = [item for item in parsed['content'] if item.get('type') == 'image']
                        print(f"DEBUG: 段落中包含 {len(para_images)} 个图片")

        # 如果div中有内容，返回一个容器类型
        if content:
            div_class = element.get('class', [])
            print(f"DEBUG: 处理div容器，class={div_class}，包含{len(content)}个子元素")
            # 递归检查是否包含图片
            def count_images_recursive(items):
                count = 0
                for item in items:
                    if isinstance(item, dict):
                        if item.get('type') == 'image':
                            count += 1
                        elif 'content' in item and isinstance(item['content'], list):
                            count += count_images_recursive(item['content'])
                return count

            image_count_in_div = count_images_recursive(content)
            print(f"DEBUG: div容器中包含 {image_count_in_div} 个图片元素（递归统计）")
            return {
                "type": "container",
                "class": div_class,
                "content": content
            }
        return None

    # 处理span元素
    elif element.name == 'span':
        # 递归处理span中的子元素
        content = []
        for child in element.children:
            if child.name:
                parsed = parse_element(child, images_dir)
                if parsed:
                    content.append(parsed)
            else:
                text = clean_text(child.string)
                if text:
                    content.append({"type": "text", "content": text})

        if content:
            return {
                "type": "span",
                "style": element.get('style', ''),
                "content": content
            }
        return None

    # 处理s元素（删除线）
    elif element.name == 's':
        # 递归处理s中的子元素
        content = []
        for child in element.children:
            if child.name:
                parsed = parse_element(child, images_dir)
                if parsed:
                    content.append(parsed)
            else:
                text = clean_text(child.string)
                if text:
                    content.append({"type": "text", "content": text})

        if content:
            return {
                "type": "strikethrough",
                "content": content
            }
        return None

    # 处理未识别元素
    return {"type": "unknown", "element": element.name}

def clean_text(text):
    """清理文本中的多余空格和换行"""
    return re.sub(r'\s+', ' ', text).strip()

def parse_paragraph(p_element, images_dir=None):
    """解析段落及其内联内容"""
    content = []
    for child in p_element.children:
        if child.name is None:  # 文本节点
            text = clean_text(child.string)
            if text:
                content.append({"type": "text", "content": text})
        elif child.name == 'strong':
            content.append({"type": "strong", "content": clean_text(child.get_text())})
        elif child.name == 'em':
            content.append({"type": "emphasis", "content": clean_text(child.get_text())})
        elif child.name == 'code':
            content.append({"type": "inline_code", "content": clean_text(child.get_text())})
        elif child.name == 'a':
            content.append(parse_link(child))
        elif child.name == 'ac:image':
            content.append(parse_image(child, images_dir))
        elif child.name == 'span':
            # 处理带样式的文本
            style = child.get('style', '')
            text = clean_text(child.get_text())
            if style and text:
                content.append({"type": "styled_text", "style": style, "content": text})
            elif text:
                content.append({"type": "text", "content": text})

    return {
        "type": "paragraph",
        "content": content
    }

def parse_list(list_element, images_dir=None):
    """解析列表（有序/无序）"""
    list_type = 'unordered' if list_element.name == 'ul' else 'ordered'
    items = []

    for item in list_element.find_all('li', recursive=False):
        item_data = parse_list_item(item, images_dir)
        if item_data:
            items.append(item_data)

    return {
        "type": "list",
        "list_type": list_type,
        "items": items
    }

def parse_list_item(item_element, images_dir=None):
    """解析单个列表项及其子元素"""
    item_content = []

    # 处理列表项中的内容
    for child in item_element.children:
        if child.name is None:  # 文本节点
            text = clean_text(child.string)
            if text:
                item_content.append({"type": "text", "content": text})
        elif child.name in ['p', 'ul', 'ol', 'div']:
            # 处理段落、子列表等块级元素
            parsed = parse_element(child, images_dir)
            if parsed:
                item_content.append(parsed)
        elif child.name in ['strong', 'em', 'code', 'a', 'ac:image']:
            # 处理内联元素
            if child.name == 'strong':
                item_content.append({"type": "strong", "content": clean_text(child.get_text())})
            elif child.name == 'em':
                item_content.append({"type": "emphasis", "content": clean_text(child.get_text())})
            elif child.name == 'code':
                item_content.append({"type": "inline_code", "content": clean_text(child.get_text())})
            elif child.name == 'a':
                item_content.append(parse_link(child))
            elif child.name == 'ac:image':
                item_content.append(parse_image(child, images_dir))

    # 检查是否有子列表
    sublists = []
    for sublist in item_element.find_all(['ul', 'ol'], recursive=False):
        sublists.append(parse_list(sublist, images_dir))

    return {
        "content": item_content,
        "sublists": sublists
    }

def parse_table(table_element, images_dir=None):
    """解析表格结构"""
    print(f"DEBUG: 开始解析表格，包含 {len(table_element.find_all('td'))} 个单元格")
    divs_in_table = table_element.find_all('div', class_='content-wrapper')
    images_in_table = table_element.find_all('ac:image')
    print(f"DEBUG: 表格中有 {len(divs_in_table)} 个div.content-wrapper, {len(images_in_table)} 个图片")

    rows = []

    # 处理表头（如果有）
    header_row = table_element.find('tr', {'class': 'highlight-header'})
    if header_row:
        headers = [clean_text(th.get_text()) for th in header_row.find_all(['th', 'td'])]
        rows.append({"type": "header", "cells": headers})

    # 处理表格主体
    for row in table_element.find_all('tr'):
        # 跳过表头行
        if row.get('class') and 'highlight-header' in row.get('class'):
            continue

        cells = []
        for cell in row.find_all(['td', 'th']):
            cell_content = []
            for child in cell.children:
                if child.name:
                    # 调试：检查表格单元格中的子元素
                    if child.name == 'div' and 'content-wrapper' in child.get('class', []):
                        print(f"DEBUG: 表格单元格中发现 div.content-wrapper，包含子元素: {[c.name for c in child.children if c.name]}")
                        images_in_div = child.find_all('ac:image')
                        print(f"DEBUG: 该div中包含 {len(images_in_div)} 个图片")

                    parsed = parse_element(child, images_dir)
                    if parsed:
                        cell_content.append(parsed)
                else:
                    text = clean_text(child.string)
                    if text:
                        cell_content.append({"type": "text", "content": text})

            # 获取单元格属性
            cell_attrs = {
                "colspan": int(cell.get('colspan', 1)),
                "rowspan": int(cell.get('rowspan', 1))
            }

            cells.append({
                "type": "cell",
                "attributes": cell_attrs,
                "content": cell_content
            })

        rows.append({"type": "row", "cells": cells})

    return {
        "type": "table",
        "rows": rows
    }

def parse_code_macro(macro_element):
    """解析代码块宏"""
    language_elem = macro_element.find('ac:parameter', {'ac:name': 'language'})
    theme_elem = macro_element.find('ac:parameter', {'ac:name': 'theme'})
    linenumbers_elem = macro_element.find('ac:parameter', {'ac:name': 'linenumbers'})
    content_elem = macro_element.find('ac:plain-text-body')

    return {
        "type": "code_block",
        "language": language_elem.get_text().strip() if language_elem else None,
        "theme": theme_elem.get_text().strip() if theme_elem else None,
        "line_numbers": linenumbers_elem.get_text().strip().lower() == 'true' if linenumbers_elem else False,
        "content": content_elem.get_text().strip() if content_elem else ""
    }

def parse_info_macro(macro_element, images_dir=None):
    """解析信息/注意/警告宏"""
    macro_type = macro_element.get('ac:name')
    title_elem = macro_element.find('ac:parameter', {'ac:name': 'title'})
    content_elem = macro_element.find('ac:rich-text-body')

    # 解析内容
    content = []
    if content_elem:
        for child in content_elem.children:
            if child.name:
                parsed = parse_element(child, images_dir)
                if parsed:
                    content.append(parsed)

    return {
        "type": "info_macro",
        "macro_type": macro_type,
        "title": clean_text(title_elem.get_text()) if title_elem else None,
        "content": content
    }

def parse_panel_macro(macro_element, images_dir=None):
    """解析面板宏"""
    title_elem = macro_element.find('ac:parameter', {'ac:name': 'title'})
    border_elem = macro_element.find('ac:parameter', {'ac:name': 'borderStyle'})
    bg_elem = macro_element.find('ac:parameter', {'ac:name': 'backgroundColor'})
    content_elem = macro_element.find('ac:rich-text-body')

    # 解析内容
    content = []
    if content_elem:
        for child in content_elem.children:
            if child.name:
                parsed = parse_element(child, images_dir)
                if parsed:
                    content.append(parsed)

    return {
        "type": "panel",
        "title": clean_text(title_elem.get_text()) if title_elem else None,
        "border_style": border_elem.get_text().strip() if border_elem else None,
        "background_color": bg_elem.get_text().strip() if bg_elem else None,
        "content": content
    }

def download_image(url, filename, images_dir):
    """下载图片到本地目录"""
    try:
        # 使用与Confluence相同的认证信息
        auth = (USERNAME, PASSWORD)

        # 发送请求下载图片
        response = requests.get(url, auth=auth, stream=True)
        response.raise_for_status()

        # 确保图片目录存在
        os.makedirs(images_dir, exist_ok=True)

        # 保存图片
        local_path = os.path.join(images_dir, filename)
        with open(local_path, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)

        print(f"✅ 图片下载成功: {filename}")
        return local_path

    except Exception as e:
        print(f"❌ 图片下载失败 {filename}: {e}")
        return None

def parse_image(image_element, images_dir=None):
    """解析图片元素并下载到本地"""
    # 获取图片URL
    ri_attachment = image_element.find('ri:attachment')
    ri_url = image_element.find('ri:url')

    if ri_attachment:
        filename = ri_attachment.get('ri:filename', '')
        # 构建完整的图片URL
        url = f"http://wiki.fengyuwl.net/download/attachments/92971254/{filename}"
        print(f"DEBUG: 成功解析图片 - {filename}")

        # 下载图片到本地
        local_url = url  # 默认使用原URL
        if images_dir and filename:
            local_path = download_image(url, filename, images_dir)
            if local_path:
                # 使用相对路径作为本地URL
                local_url = f"./images/{filename}"

    elif ri_url:
        url = ri_url.get('ri:value', '')
        print(f"DEBUG: 成功解析图片URL - {url}")
        local_url = url  # 外部URL不下载
    else:
        url = ""
        local_url = ""
        print(f"DEBUG: 图片解析失败，未找到URL")

    # 获取其他属性
    alt = image_element.find('ac:alt')
    caption = image_element.find('ac:caption')

    result = {
        "type": "image",
        "url": local_url,  # 使用本地URL
        "original_url": url if ri_attachment or ri_url else "",  # 保留原始URL
        "alt_text": alt.get_text() if alt else "",
        "caption": caption.get_text() if caption else ""
    }
    return result

def parse_attachment(link_element):
    """解析附件元素"""
    attachment = link_element.find('ri:attachment')
    if not attachment:
        return None

    file_name = attachment.get('ri:filename', '')
    page_id = attachment.get('ri:page-id', '')

    # 获取链接文本
    link_text = link_element.find('ac:plain-text-link-body')

    return {
        "type": "attachment",
        "file_name": file_name,
        "page_id": page_id,
        "link_text": link_text.get_text() if link_text else file_name
    }

def parse_link(link_element):
    """解析链接元素"""
    href = link_element.get('href', '')

    # 解析Confluence内部链接
    page_match = re.match(r'/pages/viewpage\.action\?pageId=(\d+)', href)
    page_id = page_match.group(1) if page_match else None

    return {
        "type": "link",
        "href": href,
        "page_id": page_id,
        "text": clean_text(link_element.get_text())
    }

def save_results(data, output_format='json'):
    """保存解析结果"""
    file_path = "/Users/<USER>/Downloads/"
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    # filename = f"confluence_export_{timestamp}"
    filename = file_path + timestamp

    if output_format == 'json':
        filename += ".json"
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)

    elif output_format == 'html':
        filename += ".html"
        # 简化的HTML输出，实际应用中可扩展
        html_content = "<html><head><title>Confluence Export</title></head><body>"
        html_content += f"<h1>{data['metadata']['title']}</h1>"

        for item in data['content']:
            html_content += render_html_element(item)

        html_content += "</body></html>"

        with open(filename, 'w', encoding='utf-8') as f:
            f.write(html_content)

    # print(f"Results saved to: {os.path.abspath(filename)}")
    print(f"Results saved to: {filename}")
    return filename

def render_html_element(element):
    """将解析的元素渲染为HTML（用于演示）"""
    if element['type'] == 'heading':
        return f"<h{element['level']}>{element['text']}</h{element['level']}>"

    elif element['type'] == 'paragraph':
        content = ''.join([render_html_element(c) for c in element['content']])
        return f"<p>{content}</p>"

    elif element['type'] == 'text':
        return element['content']

    elif element['type'] == 'strong':
        return f"<strong>{element['content']}</strong>"

    # 其他元素类型的渲染...
    return f"<!-- Unsupported element: {element['type']} -->"


# ========== 呈现功能 ==========
def render_to_markdown(data):
    """将解析内容转换为Markdown格式"""
    md_lines = []

    # 添加元数据头部
    md_lines.append(f"# {data['metadata']['title']}\n")
    md_lines.append(f"> **ID:** {data['metadata']['page_id']}  ")
    md_lines.append(f"> **版本:** {data['metadata']['version']}  ")
    md_lines.append(f"> **创建者:** {data['metadata']['creator']}  ")
    md_lines.append(f"> **创建时间:** {data['metadata']['created_date']}  ")
    md_lines.append(f"> **最后修改:** {data['metadata']['last_modified_by']} at {data['metadata']['last_modified_date']}\n")

    # 处理内容元素
    for item in data['content']:
        md_lines.append(render_element_to_md(item))

    return "\n".join(md_lines)

def render_element_to_md(element):
    """将单个元素转换为Markdown"""
    if element['type'] == 'heading':
        return f"{'#' * element['level']} {element['text']}\n"

    elif element['type'] == 'paragraph':
        content = "".join([render_inline_to_md(inline) for inline in element.get('content', [])])
        return f"{content}\n"

    elif element['type'] == 'list':
        list_md = []
        list_char = "-" if element['list_type'] == 'unordered' else "1."

        for item in element['items']:
            list_md.extend(render_list_item_to_md(item, list_char, 0))

        return "\n".join(list_md) + "\n"

    elif element['type'] == 'table':
        table_md = []
        header_processed = False

        for i, row in enumerate(element['rows']):
            if row['type'] == 'header':
                # 处理表头行
                header_cells = []
                for cell in row['cells']:
                    if isinstance(cell, str):
                        header_cells.append(cell)
                    else:
                        # cell是对象，需要渲染其内容
                        cell_content = render_cell_content(cell)
                        header_cells.append(cell_content)

                table_md.append("| " + " | ".join(header_cells) + " |")
                table_md.append("| " + " | ".join(["---"] * len(header_cells)) + " |")
                header_processed = True
            else:
                # 处理普通行
                row_cells = []
                for cell in row['cells']:
                    cell_content = render_cell_content(cell)
                    # 清理单元格内容中的换行符和管道符，避免破坏表格格式
                    cell_content = cell_content.replace('\n', ' ').replace('|', '\\|')
                    row_cells.append(cell_content)

                # 如果没有表头，第一行作为普通行但添加分隔符
                if not header_processed and i == 0:
                    table_md.append("| " + " | ".join(row_cells) + " |")
                    table_md.append("| " + " | ".join(["---"] * len(row_cells)) + " |")
                    header_processed = True
                else:
                    table_md.append("| " + " | ".join(row_cells) + " |")

        return "\n".join(table_md) + "\n"

    elif element['type'] == 'code_block':
        lang = element.get('language', '')
        return f"```{lang}\n{element['content']}\n```\n"

    elif element['type'] == 'info_macro':
        macro_type = element['macro_type'].upper()
        title = element.get('title', '')
        content = "\n".join([render_element_to_md(c) for c in element.get('content', [])])
        return f"> **{macro_type}**: {title}\n>\n" + "\n".join([f"> {line}" for line in content.split("\n")]) + "\n\n"

    elif element['type'] == 'image':
        alt = element.get('alt_text', 'image')
        return f"![{alt}]({element['url']})"

    elif element['type'] == 'attachment':
        return f"[{element['link_text']}](attachment:{element['file_name']})"

    elif element['type'] == 'link':
        return f"[{element['text']}]({element['href']})"

    elif element['type'] == 'container':
        # 渲染容器中的所有子元素
        content_md = ""
        for child in element.get('content', []):
            content_md += render_element_to_md(child)
        return content_md

    elif element['type'] == 'span':
        # 渲染span中的所有子元素
        content_md = ""
        for child in element.get('content', []):
            if isinstance(child, dict):
                if child.get('type') == 'text':
                    content_md += child.get('content', '')
                else:
                    content_md += render_inline_to_md(child)
            else:
                content_md += str(child)
        return content_md

    elif element['type'] == 'strikethrough':
        # 渲染删除线中的所有子元素
        content_md = ""
        for child in element.get('content', []):
            if isinstance(child, dict):
                if child.get('type') == 'text':
                    content_md += child.get('content', '')
                else:
                    content_md += render_inline_to_md(child)
            else:
                content_md += str(child)
        return f"~~{content_md}~~"

    return f"<!-- Unsupported element: {element['type']} -->\n"

def render_cell_content(cell):
    """处理表格单元格内容，支持不同的内容类型"""
    content = cell.get('content', [])

    # 如果content为空，返回空字符串
    if not content:
        return ""

    # 如果content是字符串，直接返回
    if isinstance(content, str):
        return content

    # 如果content是列表，需要根据元素类型处理
    if isinstance(content, list):
        result_parts = []
        for item in content:
            if isinstance(item, str):
                result_parts.append(item)
            elif isinstance(item, dict):
                if item.get('type') == 'text':
                    # 直接的文本内容
                    result_parts.append(item.get('content', ''))
                elif item.get('type') == 'paragraph':
                    # 段落内容，需要递归处理其内容
                    para_content = item.get('content', [])
                    para_text = "".join([render_inline_to_md(c) for c in para_content])
                    result_parts.append(para_text)
                elif item.get('type') in ['styled_text', 'strong', 'emphasis', 'inline_code']:
                    # 内联样式元素
                    result_parts.append(render_inline_to_md(item))
                elif item.get('type') == 'unknown':
                    # 未知元素，返回空字符串或占位符
                    result_parts.append("")
                else:
                    # 其他块级元素，使用render_element_to_md处理
                    element_result = render_element_to_md(item)
                    # 移除末尾的换行符，因为在表格中不需要
                    result_parts.append(element_result.rstrip('\n'))
            else:
                result_parts.append(str(item))

        return "".join(result_parts)

    # 其他情况，转换为字符串
    return str(content)

def render_list_item_to_md(item, list_char, level):
    """递归渲染列表项"""
    indent = "  " * level
    lines = []

    # 主项内容 - 需要处理混合内容（内联元素和块级元素）
    content_parts = []
    for c in item.get('content', []):
        if isinstance(c, str):
            content_parts.append(c)
        elif isinstance(c, dict):
            if c.get('type') in ['text', 'strong', 'emphasis', 'inline_code', 'styled_text', 'link', 'image']:
                # 内联元素
                content_parts.append(render_inline_to_md(c))
            elif c.get('type') in ['paragraph', 'list']:
                # 块级元素，需要特殊处理
                if c.get('type') == 'paragraph':
                    # 段落内容
                    para_content = "".join([render_inline_to_md(pc) for pc in c.get('content', [])])
                    content_parts.append(para_content)
                elif c.get('type') == 'list':
                    # 嵌套列表，先添加当前内容，然后处理嵌套列表
                    if content_parts:
                        prefix = f"{indent}{list_char} " if level == 0 else f"{indent}- "
                        lines.append(f"{prefix}{''.join(content_parts)}")
                        content_parts = []

                    # 处理嵌套列表
                    nested_char = "-" if c['list_type'] == 'unordered' else "1."
                    for nested_item in c['items']:
                        lines.extend(render_list_item_to_md(nested_item, nested_char, level + 1))
            else:
                # 其他类型，尝试渲染为元素
                element_result = render_element_to_md(c)
                content_parts.append(element_result.rstrip('\n'))
        else:
            content_parts.append(str(c))

    # 如果还有剩余内容，添加到列表中
    if content_parts:
        prefix = f"{indent}{list_char} " if level == 0 else f"{indent}- "
        lines.append(f"{prefix}{''.join(content_parts)}")

    # 子列表
    for sublist in item.get('sublists', []):
        sub_char = "-" if sublist['type'] == 'unordered' else "1."
        for subitem in sublist['items']:
            lines.extend(render_list_item_to_md(subitem, sub_char, level + 1))

    return lines

def render_inline_to_md(inline):
    """渲染内联元素"""
    if isinstance(inline, str):  # 如果已经是字符串，直接返回
        return inline

    if not isinstance(inline, dict):
        print(f"DEBUG: render_inline_to_md 收到非字典类型: {type(inline)}, 值: {inline}")
        return str(inline)

    if inline['type'] == 'text':
        return inline['content']
    elif inline['type'] == 'strong':
        return f"**{inline['content']}**"
    elif inline['type'] == 'emphasis':
        return f"*{inline['content']}*"
    elif inline['type'] == 'inline_code':
        return f"`{inline['content']}`"
    elif inline['type'] == 'styled_text':
        # 处理带样式的文本，忽略样式信息，只返回内容
        return inline.get('content', '')
    elif inline['type'] == 'link':
        return f"[{inline['text']}]({inline['href']})"
    elif inline['type'] == 'image':
        return f"![{inline.get('alt_text', '')}]({inline['url']})"
    elif inline['type'] == 'strikethrough':
        # 处理删除线，递归渲染其内容
        content_md = ""
        for child in inline.get('content', []):
            if isinstance(child, dict):
                content_md += render_inline_to_md(child)
            else:
                content_md += str(child)
        return f"~~{content_md}~~"
    elif inline['type'] == 'span':
        # 处理span元素，递归渲染其内容
        content_md = ""
        for child in inline.get('content', []):
            if isinstance(child, dict):
                content_md += render_inline_to_md(child)
            else:
                content_md += str(child)
        return content_md
    elif inline['type'] == 'unknown':
        # 未知类型返回空字符串
        return ""
    else:
        print(f"DEBUG: render_inline_to_md 未处理的类型: {inline.get('type', 'no_type')}, 内容: {inline}")
        return inline.get('content', '')


if __name__ == "__main__":
    page_url = "http://wiki.fengyuwl.net/pages/viewpage.action?pageId=92971254"
    print("Starting Confluence Wiki content parser...")
    # print(f"Retrieving page: {PAGE_TITLE} in space: {SPACE_KEY}")

    # 创建输出目录和图片目录
    import datetime
    import os
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    output_dir = "/Users/<USER>/Downloads"
    images_dir = os.path.join(output_dir, "images")

    # 确保目录存在
    os.makedirs(output_dir, exist_ok=True)
    os.makedirs(images_dir, exist_ok=True)

    # 解析页面内容，传递图片目录
    result = parse_page_content(images_dir)

    if 'error' in result:
        print(f"Error: {result['error']}")
    else:
        print("Successfully parsed page content!")
        print(f"Page ID: {result['metadata']['page_id']}")
        print(f"Version: {result['metadata']['version']}")
        print(f"Created by: {result['metadata']['creator']}")

        # 保存结果
        # save_results(result, 'json')
        # save_results(result, 'html')

        # 转换为Markdown并保存到文件
        print("\n转换为Markdown并保存到文件...")
        markdown_content = render_to_markdown(result)

        # 生成文件名：{pageid}_{timestamp}.md
        page_id = result['metadata']['page_id']

        # 清理页面标题作为文件名的一部分（可选）
        page_title = result['metadata'].get('title', '').strip()
        if page_title:
            # 移除文件名中不允许的字符
            safe_title = "".join(c for c in page_title if c.isalnum() or c in (' ', '-', '_')).rstrip()
            safe_title = safe_title.replace(' ', '_')[:20]  # 限制长度
            filename = f"{page_id}_{safe_title}_{timestamp}.md"
        else:
            filename = f"{page_id}_{timestamp}.md"

        filepath = os.path.join(output_dir, filename)

        # 写入文件
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(markdown_content)

            # 获取文件大小
            file_size = os.path.getsize(filepath)
            file_size_kb = file_size / 1024

            print(f"✅ Markdown文件已保存到: {filepath}")
            print(f"📄 文件大小: {file_size_kb:.1f} KB ({file_size} bytes)")
            print(f"📝 内容长度: {len(markdown_content)} 字符")

        except Exception as e:
            print(f"❌ 保存文件时出错: {e}")
            # 如果保存失败，仍然打印到控制台
            print("\n转换的Markdown：\n")
            print(markdown_content)

        # 显示解析统计信息
        content_types = {}
        image_count = [0]  # 使用列表来避免nonlocal问题

        def count_elements(elements):
            for item in elements:
                if not isinstance(item, dict):
                    continue

                t = item.get('type', 'unknown')
                content_types[t] = content_types.get(t, 0) + 1

                print(f"DEBUG: 统计元素类型: {t}")

                if t == 'image':
                    image_count[0] += 1
                    print(f"DEBUG: 统计到图片元素，当前总数: {image_count[0]}")
                elif t == 'container':
                    print(f"DEBUG: 统计到容器元素，包含 {len(item.get('content', []))} 个子元素")

                # 递归检查子元素
                if 'content' in item and isinstance(item['content'], list):
                    count_elements(item['content'])
                if 'items' in item and isinstance(item['items'], list):
                    for subitem in item['items']:
                        if isinstance(subitem, dict) and 'content' in subitem and isinstance(subitem['content'], list):
                            count_elements(subitem['content'])
                # 处理表格结构
                if 'rows' in item and isinstance(item['rows'], list):
                    for row in item['rows']:
                        if isinstance(row, dict) and 'cells' in row and isinstance(row['cells'], list):
                            for cell in row['cells']:
                                if isinstance(cell, dict) and 'content' in cell and isinstance(cell['content'], list):
                                    count_elements(cell['content'])

        count_elements(result['content'])

        print("\nContent Summary:")
        for t, count in content_types.items():
            print(f"- {t}: {count} element(s)")

        print(f"总共发现 {image_count[0]} 个图片元素")

        print("\nParsing completed successfully!")
